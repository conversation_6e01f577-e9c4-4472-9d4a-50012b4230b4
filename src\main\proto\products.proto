syntax = "proto3";

option java_package = "com.example.grpc";
option java_outer_classname = "ProductServiceProto";

service ProductService {
  rpc GetProductById(ProductIdRequest) returns (ProductResponse);
  rpc GetAllProducts(Empty) returns (ProductsResponse);
  rpc CreateProduct(CreateProductRequest) returns (ProductResponse);
  rpc UpdateProduct(UpdateProductRequest) returns (ProductResponse);
  rpc DeleteProduct(ProductIdRequest) returns (DeleteResponse);
}

message Empty {}

message ProductIdRequest {
  int32 id = 1;
}

message Product {
  int32 id = 1;
  string name = 2;
  string description = 3;
  double price = 4;
}

message ProductResponse {
  Product product = 1;
}

message ProductsResponse {
  repeated Product products = 1;
}

message CreateProductRequest {
  string name = 1;
  string description = 2;
  double price = 3;
}

message UpdateProductRequest {
  int32 id = 1;
  string name = 2;
  string description = 3;
  double price = 4;
}

message DeleteResponse {
  bool success = 1;
  string message = 2;
}
