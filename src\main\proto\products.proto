syntax = "proto3";

option java_package = "com.example.grpc";
option java_outer_classname = "ProductServiceProto";

service ProductService {
  rpc GetById(Id) returns (Product);
  rpc GetAll(Empty) returns (ProductList);
  rpc Create(Product) returns (Product);
  rpc Update(Product) returns (Product);
  rpc Delete(Id) returns (Result);
}

message Empty {}

message Id {
  int32 id = 1;
}

message Product {
  int32 id = 1;
  string name = 2;
  string description = 3;
  double price = 4;
}

message ProductList {
  repeated Product products = 1;
}

message Result {
  bool success = 1;
  string message = 2;
}
