spring.application.name=demo
spring.datasource.url=**************************************
spring.datasource.username=root
spring.datasource.password=!@#Zayed
# gRPC Server Configuration
server.port=8088


# gRPC Server Settings
grpc.server.port=9090
grpc.server.address=0.0.0.0

# gRPC Client Settings
grpc.server.host=localhost


# Spring Web Server

## ????? ??????? ???????? ?? JPA ?DataSource
#spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration,org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration