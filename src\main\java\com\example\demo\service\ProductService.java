package com.example.demo.service;

import com.example.demo.entity.Product;
import com.example.demo.repos.ProductRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class ProductService {

    private final ProductRepository productRepository;

    public ProductService(ProductRepository repo) {
        this.productRepository = repo;
    }

    public Optional<Product> getById(int id) { return productRepository.findById(id); }
    public List<Product> getAll() { return productRepository.findAll(); }
    public Product create(Product product) { return productRepository.save(product); }
    public Product update(Product product) { return productRepository.save(product); }
    public boolean delete(int id) {
        if (productRepository.existsById(id)) {
            productRepository.deleteById(id);
            return true;
        }
        return false;
    }
}
