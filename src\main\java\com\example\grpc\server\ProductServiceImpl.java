package com.example.grpc.server;

import com.example.demo.service.ProductService;
import com.example.demo.entity.Product;
import com.example.grpc.ProductServiceProto;
import com.example.grpc.ProductServiceProto.*;
import com.example.grpc.ProductServiceGrpc;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import net.devh.boot.grpc.server.service.GrpcService;

import java.util.List;
import java.util.stream.Collectors;

@GrpcService
public class ProductServiceImpl extends ProductServiceGrpc.ProductServiceImplBase {

    private final ProductService productService;

    public ProductServiceImpl(ProductService productService) {
        this.productService = productService;
    }

    @Override
    public void getProductById(ProductIdRequest request, StreamObserver<ProductResponse> responseObserver) {
        productService.getById(request.getId()).ifPresentOrElse(product -> {
            responseObserver.onNext(ProductResponse.newBuilder().setProduct(convertToProto(product)).build());
            responseObserver.onCompleted();
        }, () -> {
            responseObserver.onError(Status.NOT_FOUND.withDescription("Product with ID " + request.getId() + " not found").asRuntimeException());
        });
    }

    @Override
    public void getAllProducts(Empty request, StreamObserver<ProductsResponse> responseObserver) {
        List<Product> products = productService.getAll();
        ProductsResponse response = ProductsResponse.newBuilder()
                .addAllProducts(products.stream().map(this::convertToProto).collect(Collectors.toList()))
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void createProduct(CreateProductRequest request, StreamObserver<ProductResponse> responseObserver) {
        Product product = new Product();
        product.setName(request.getName());
        product.setDescription(request.getDescription());
        product.setPrice(request.getPrice());

        Product created = productService.create(product);
        responseObserver.onNext(ProductResponse.newBuilder().setProduct(convertToProto(created)).build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateProduct(UpdateProductRequest request, StreamObserver<ProductResponse> responseObserver) {
        productService.getById(request.getId()).ifPresentOrElse(existing -> {
            existing.setName(request.getName());
            existing.setDescription(request.getDescription());
            existing.setPrice(request.getPrice());

            Product updated = productService.update(existing);
            responseObserver.onNext(ProductResponse.newBuilder().setProduct(convertToProto(updated)).build());
            responseObserver.onCompleted();
        }, () -> {
            responseObserver.onError(Status.NOT_FOUND.withDescription("Product not found").asRuntimeException());
        });
    }

    @Override
    public void deleteProduct(ProductIdRequest request, StreamObserver<DeleteResponse> responseObserver) {
        boolean deleted = productService.delete(request.getId());
        DeleteResponse response = DeleteResponse.newBuilder()
                .setSuccess(deleted)
                .setMessage(deleted ? "Deleted successfully" : "Product not found")
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    private ProductServiceProto.Product convertToProto(Product product) {
        return ProductServiceProto.Product.newBuilder()
                .setId(product.getId())
                .setName(product.getName())
                .setDescription(product.getDescription())
                .setPrice(product.getPrice())
                .build();
    }
}
