//package com.example.grpc.server;
//
//import com.example.demo.service.ProductService;
//import com.example.demo.entity.Product;
//import com.example.grpc.ProductServiceProto;
//
//import com.example.grpc.ProductServiceGrpc;
//import io.grpc.Status;
//import io.grpc.stub.StreamObserver;
//import net.devh.boot.grpc.server.service.GrpcService;
//
//@GrpcService
//public class ProductServiceImpl extends ProductServiceGrpc.ProductServiceImplBase {
//
//    private final ProductService productService;
//
//    public ProductServiceImpl(ProductService service) {
//        this.productService = service;
//    }
//
//    private ProductServiceProto.Product toProto(Product p) {
//        return ProductServiceProto.Product.newBuilder()
//                .setId(p.getId())
//                .setName(p.getName())
//                .setDescription(p.getDescription())
//                .setPrice(p.getPrice())
//                .build();
//    }
//
//    private Product fromProto(ProductServiceProto.Product p) {
//        Product product = new Product();
//        product.setId(p.getId());
//        product.setName(p.getName());
//        product.setDescription(p.getDescription());
//        product.setPrice(p.getPrice());
//        return product;
//    }
//
//    @Override
//    public void getById(ProductServiceProto.Id request, StreamObserver<ProductServiceProto.Product> responseObserver) {
//        productService.getById(request.getId()).ifPresentOrElse(
//                p -> {
//                    responseObserver.onNext(toProto(p));
//                    responseObserver.onCompleted();
//                },
//                () -> responseObserver.onError(Status.NOT_FOUND.asRuntimeException())
//        );
//    }
//
//    @Override
//    public void getAll(ProductServiceProto.Empty req, StreamObserver<ProductServiceProto.ProductList> obs) {
//        var products = productService.getAll().stream().map(this::toProto).toList();
//        obs.onNext(ProductServiceProto.ProductList.newBuilder().addAllProducts(products).build());
//        obs.onCompleted();
//    }
//
//    @Override
//    public void create(ProductServiceProto.Product req, StreamObserver<ProductServiceProto.Product> obs) {
//        var created = productService.create(fromProto(req));
//        obs.onNext(toProto(created));
//        obs.onCompleted();
//    }
//
//    @Override
//    public void update(ProductServiceProto.Product req, StreamObserver<ProductServiceProto.Product> obs) {
//        var updated = productService.update(fromProto(req));
//        obs.onNext(toProto(updated));
//        obs.onCompleted();
//    }
//
//    @Override
//    public void delete(ProductServiceProto.Id req, StreamObserver<ProductServiceProto.Result> obs) {
//        boolean deleted = productService.delete(req.getId());
//        obs.onNext(ProductServiceProto.Result.newBuilder()
//                .setSuccess(deleted)
//                .setMessage(deleted ? "Deleted" : "Not found").build());
//        obs.onCompleted();
//    }
//}
