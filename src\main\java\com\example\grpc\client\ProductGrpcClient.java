package com.example.grpc.client;

import com.example.grpc.ProductServiceGrpc;
import com.example.grpc.ProductServiceProto;
import com.example.grpc.ProductServiceProto.*;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.example.demo.entity.Product;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ProductGrpcClient {

    private final ManagedChannel channel;
    private final ProductServiceGrpc.ProductServiceBlockingStub blockingStub;

    public ProductGrpcClient(@Value("${grpc.server.host:localhost}") String host,
                             @Value("${grpc.server.port}") int port) {
        this.channel = ManagedChannelBuilder.forAddress(host, port)
                .usePlaintext()
                .build();
        this.blockingStub = ProductServiceGrpc.newBlockingStub(channel);
    }

    public ProductServiceProto.Product getProductById(int id) {
        ProductIdRequest request = ProductIdRequest.newBuilder().setId(id).build();
        ProductResponse response = blockingStub.getProductById(request);
        return response.getProduct();
    }

    public List<Product> getAllProducts() {
        ProductsResponse response = blockingStub.getAllProducts(Empty.newBuilder().build());
        return response.getProductsList().stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());
    }

    public ProductServiceProto.Product createProduct(String name, String description, double price) {
        CreateProductRequest request = CreateProductRequest.newBuilder()
                .setName(name)
                .setDescription(description)
                .setPrice(price)
                .build();
        ProductResponse response = blockingStub.createProduct(request);
        return response.getProduct();
    }

    public ProductServiceProto.Product updateProduct(int id, String name, String description, double price) {
        UpdateProductRequest request = UpdateProductRequest.newBuilder()
                .setId(id)
                .setName(name)
                .setDescription(description)
                .setPrice(price)
                .build();
        ProductResponse response = blockingStub.updateProduct(request);
        return response.getProduct();
    }

    public DeleteResponse deleteProduct(int id) {
        ProductIdRequest request = ProductIdRequest.newBuilder().setId(id).build();
        return blockingStub.deleteProduct(request);
    }

    private Product convertToEntity(ProductServiceProto.Product protoProduct) {
        Product product = new Product();
        product.setId(protoProduct.getId());
        product.setName(protoProduct.getName());
        product.setDescription(protoProduct.getDescription());
        product.setPrice(protoProduct.getPrice());
        return product;
    }
}
