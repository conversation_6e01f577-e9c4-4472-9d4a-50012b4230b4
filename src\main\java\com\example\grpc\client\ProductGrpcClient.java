//package com.example.grpc.client;
//
//import com.example.grpc.ProductServiceGrpc;
//import com.example.grpc.ProductServiceProto;
//
//import io.grpc.ManagedChannelBuilder;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Service;
//import com.example.demo.entity.Product;
//
//import java.util.List;
//@Service
//public class ProductGrpcClient {
//
//    private final ProductServiceGrpc.ProductServiceBlockingStub stub;
//
//    public ProductGrpcClient(@Value("${grpc.server.host:localhost}") String host,
//                             @Value("${grpc.server.port}") int port) {
//        stub = ProductServiceGrpc.newBlockingStub(
//                ManagedChannelBuilder.forAddress(host, port).usePlaintext().build());
//    }
//
//    public Product getById(int id) {
//        return fromProto(stub.getById(ProductServiceProto.Id.newBuilder().setId(id).build()));
//    }
//
//    public List<Product> getAll() {
//        return stub.getAll(ProductServiceProto.Empty.newBuilder().build())
//                .getProductsList().stream().map(this::fromProto).toList();
//    }
//
//    public Product create(Product p) {
//        return fromProto(stub.create(toProto(p)));
//    }
//
//    public Product update(Product p) {
//        return fromProto(stub.update(toProto(p)));
//    }
//
//    public boolean delete(int id) {
//        return stub.delete(ProductServiceProto.Id.newBuilder().setId(id).build()).getSuccess();
//    }
//
//    private Product fromProto(ProductServiceProto.Product proto) {
//        Product p = new Product();
//        p.setId(proto.getId());
//        p.setName(proto.getName());
//        p.setDescription(proto.getDescription());
//        p.setPrice(proto.getPrice());
//        return p;
//    }
//
//    private ProductServiceProto.Product toProto(Product p) {
//        return ProductServiceProto.Product.newBuilder()
//                .setId(p.getId())
//                .setName(p.getName())
//                .setDescription(p.getDescription())
//                .setPrice(p.getPrice())
//                .build();
//    }
//}
