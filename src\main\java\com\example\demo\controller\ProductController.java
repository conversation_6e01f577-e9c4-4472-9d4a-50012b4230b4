package com.example.demo.controller;


import com.example.grpc.client.ProductGrpcClient;
import com.example.demo.entity.Product;
import org.springframework.web.bind.annotation.*;

import java.util.List;
@RestController
@RequestMapping("/api/products")
public class ProductController {

    private final ProductGrpcClient client;

    public ProductController(ProductGrpcClient client) {
        this.client = client;
    }

    @GetMapping("/{id}")
    public Product get(@PathVariable int id) {
        return client.getById(id);
    }

    @GetMapping
    public List<Product> list() {
        return client.getAll();
    }

    @PostMapping
    public Product create(@RequestBody Product p) {
        return client.create(p);
    }

    @PutMapping("/{id}")
    public Product update(@PathVariable int id, @RequestBody Product p) {
        p.setId(id);
        return client.update(p);
    }


    @DeleteMapping("/{id}")
    public String delete(@PathVariable int id) {
        return client.delete(id) ? "Deleted" : "Not found";
    }
}
