package com.example.demo.controller;

import com.example.grpc.ProductServiceProto;
import com.example.grpc.client.ProductGrpcClient;
import com.example.demo.entity.Product;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/products")
public class ProductController {

    private final ProductGrpcClient productGrpcClient;

    public ProductController(ProductGrpcClient productGrpcClient) {
        this.productGrpcClient = productGrpcClient;
    }

    @GetMapping("/{id}")
    public Product getProductById(@PathVariable int id) {
        ProductServiceProto.Product protoProduct = productGrpcClient.getProductById(id);
        return convertProtoToEntity(protoProduct);
    }

    @GetMapping
    public List<Product> getAllProducts() {
        return productGrpcClient.getAllProducts();
    }

    @PostMapping
    public Product createProduct(@RequestBody ProductRequest request) {
        ProductServiceProto.Product protoProduct = productGrpcClient.createProduct(
                request.getName(),
                request.getDescription(),
                request.getPrice()
        );
        return convertProtoToEntity(protoProduct);
    }

    @PutMapping("/{id}")
    public Product updateProduct(@PathVariable int id, @RequestBody ProductRequest request) {
        ProductServiceProto.Product protoProduct = productGrpcClient.updateProduct(
                id,
                request.getName(),
                request.getDescription(),
                request.getPrice()
        );
        return convertProtoToEntity(protoProduct);
    }

    @DeleteMapping("/{id}")
    public String deleteProduct(@PathVariable int id) {
        ProductServiceProto.DeleteResponse response = productGrpcClient.deleteProduct(id);
        return response.getSuccess() ? "Product deleted successfully" : "Failed to delete product";
    }

    private Product convertProtoToEntity(ProductServiceProto.Product proto) {
        Product product = new Product();
        product.setId(proto.getId());
        product.setName(proto.getName());
        product.setDescription(proto.getDescription());
        product.setPrice(proto.getPrice());
        return product;
    }

    public static class ProductRequest {
        private String name;
        private String description;
        private double price;

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public double getPrice() { return price; }
        public void setPrice(double price) { this.price = price; }
    }
}
