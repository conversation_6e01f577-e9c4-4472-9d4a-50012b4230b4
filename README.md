# Spring Boot with gRPC - Product Management System

## Overview

This project is a Spring Boot application that uses gRPC for inter-service communication, with MySQL database for product management. The project provides both REST API and gRPC API for CRUD operations on products.

## Project Structure

```
demo/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── example/
│   │   │           ├── demo/
│   │   │           │   ├── DemoApplication.java
│   │   │           │   ├── controller/
│   │   │           │   │   └── ProductController.java
│   │   │           │   ├── entity/
│   │   │           │   │   └── Product.java
│   │   │           │   ├── repos/
│   │   │           │   │   └── ProductRepository.java
│   │   │           │   └── service/
│   │   │           │       └── ProductService.java
│   │   │           └── grpc/
│   │   │               ├── client/
│   │   │               │   └── ProductGrpcClient.java
│   │   │               └── server/
│   │   │                   └── ProductServiceImpl.java
│   │   ├── proto/
│   │   │   └── products.proto
│   │   └── resources/
│   │       └── application.properties
│   └── test/
├── target/
├── pom.xml
├── mvnw
├── mvnw.cmd
└── README.md
```

## Technologies Used

### Core Framework

- **Spring Boot 3.5.3** - Main framework
- **Java 17** - Programming language
- **Maven** - Dependency management and build tool

### Database

- **MySQL** - Primary database
- **Spring Data JPA** - Database interaction
- **Hibernate** - ORM

### gRPC

- **gRPC 1.62.2** - Inter-service communication
- **Protocol Buffers 3.25.1** - Data serialization
- **grpc-spring-boot-starter 2.15.0** - gRPC Spring Boot integration

### Additional Tools

- **Lombok** - Reduce boilerplate code
- **Spring Boot DevTools** - Development tools

## Dependencies

### Core Dependencies

```xml
<!-- Spring Boot Starters -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-jpa</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>

<!-- Database -->
<dependency>
    <groupId>com.mysql</groupId>
    <artifactId>mysql-connector-j</artifactId>
    <scope>runtime</scope>
</dependency>

<!-- Development Tools -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-devtools</artifactId>
    <scope>runtime</scope>
    <optional>true</optional>
</dependency>
<dependency>
    <groupId>org.projectlombok</groupId>
    <artifactId>lombok</artifactId>
    <optional>true</optional>
</dependency>

<!-- Testing -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>
```

### gRPC Dependencies

```xml
<!-- gRPC Core Dependencies -->
<dependency>
    <groupId>io.grpc</groupId>
    <artifactId>grpc-netty-shaded</artifactId>
    <version>1.62.2</version>
</dependency>
<dependency>
    <groupId>io.grpc</groupId>
    <artifactId>grpc-protobuf</artifactId>
    <version>1.62.2</version>
</dependency>
<dependency>
    <groupId>io.grpc</groupId>
    <artifactId>grpc-stub</artifactId>
    <version>1.62.2</version>
</dependency>

<!-- Protocol Buffers -->
<dependency>
    <groupId>com.google.protobuf</groupId>
    <artifactId>protobuf-java</artifactId>
    <version>3.25.1</version>
</dependency>
<dependency>
    <groupId>com.google.protobuf</groupId>
    <artifactId>protobuf-java-util</artifactId>
    <version>3.25.1</version>
</dependency>

<!-- Annotations -->
<dependency>
    <groupId>javax.annotation</groupId>
    <artifactId>javax.annotation-api</artifactId>
    <version>1.3.2</version>
</dependency>

<!-- Spring Boot gRPC Integration -->
<dependency>
    <groupId>net.devh</groupId>
    <artifactId>grpc-spring-boot-starter</artifactId>
    <version>2.15.0.RELEASE</version>
</dependency>
```

## Application Configuration (application.properties)

```properties
# Application name
spring.application.name=demo

# Database configuration
spring.datasource.url=**************************************
spring.datasource.username=root
spring.datasource.password=!@#Zayed

# Web server configuration
server.port=8088

# gRPC server configuration
grpc.server.port=9090
grpc.server.address=0.0.0.0

# gRPC client configuration
grpc.server.host=localhost
```

## Protocol Buffers Definition (products.proto)

```protobuf
syntax = "proto3";

option java_package = "com.example.grpc";
option java_outer_classname = "ProductServiceProto";

service ProductService {
  rpc GetProductById(ProductIdRequest) returns (ProductResponse);
  rpc GetAllProducts(Empty) returns (ProductsResponse);
  rpc CreateProduct(CreateProductRequest) returns (ProductResponse);
  rpc UpdateProduct(UpdateProductRequest) returns (ProductResponse);
  rpc DeleteProduct(ProductIdRequest) returns (DeleteResponse);
}

message Empty {}

message ProductIdRequest {
  int32 id = 1;
}

message Product {
  int32 id = 1;
  string name = 2;
  string description = 3;
  double price = 4;
}

message ProductResponse {
  Product product = 1;
}

message ProductsResponse {
  repeated Product products = 1;
}

message CreateProductRequest {
  string name = 1;
  string description = 2;
  double price = 3;
}

message UpdateProductRequest {
  int32 id = 1;
  string name = 2;
  string description = 3;
  double price = 4;
}

message DeleteResponse {
  bool success = 1;
  string message = 2;
}
```

## Complete Source Code

### 1. Main Application Class (DemoApplication.java)

**Description:** This is the main entry point of the Spring Boot application. It configures component scanning for both the demo package and gRPC package to ensure all beans are properly detected and initialized.

**Key Features:**

- `@SpringBootApplication` annotation enables auto-configuration, component scanning, and configuration
- `scanBasePackages` explicitly includes both demo and gRPC packages
- `main` method starts the Spring Boot application

```java
package com.example.demo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication(scanBasePackages = {"com.example.demo", "com.example.grpc"})
public class DemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(DemoApplication.class, args);
    }
}
```

### 2. Product Entity (Product.java)

**Description:** JPA entity class representing a product in the database. This class defines the structure and mapping for the products table.

**Key Features:**

- `@Entity` annotation marks this as a JPA entity
- `@Id` and `@GeneratedValue` configure auto-incrementing primary key
- Contains product attributes: id, name, description, price
- Provides both parameterized and default constructors
- Includes standard getter and setter methods for all fields

```java
package com.example.demo.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;

@Entity
public class Product {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    private String name;
    private String description;
    private double price;

    public Product(int id, String name, String description, double price) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.price = price;
    }

    public Product() {
    }

    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public double getPrice() { return price; }
    public void setPrice(double price) { this.price = price; }
}
```

### 3. Product Repository (ProductRepository.java)

**Description:** Spring Data JPA repository interface for Product entity. Provides automatic implementation of common database operations without writing boilerplate code.

**Key Features:**

- Extends `JpaRepository<Product, Integer>` for automatic CRUD operations
- Inherits methods like `findAll()`, `findById()`, `save()`, `deleteById()`, etc.
- Spring Data JPA automatically generates implementation at runtime
- No custom methods needed for basic operations

```java
package com.example.demo.repos;

import com.example.demo.entity.Product;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ProductRepository extends JpaRepository<Product, Integer> {
}
```

### 4. Product Service (ProductService.java)

**Description:** Business logic layer that handles product operations. Acts as an intermediary between the repository layer and the presentation layer (controllers/gRPC services).

**Key Features:**

- `@Service` annotation marks this as a Spring service component
- Constructor injection for ProductRepository dependency
- Provides business methods for all CRUD operations
- `getById()` returns Optional to handle non-existent products gracefully
- `delete()` method includes existence check and returns boolean for success/failure
- Encapsulates business logic and data access operations

```java
package com.example.demo.service;

import com.example.demo.entity.Product;
import com.example.demo.repos.ProductRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class ProductService {

    private final ProductRepository productRepository;

    public ProductService(ProductRepository repo) {
        this.productRepository = repo;
    }

    public Optional<Product> getById(int id) {
        return productRepository.findById(id);
    }

    public List<Product> getAll() {
        return productRepository.findAll();
    }

    public Product create(Product product) {
        return productRepository.save(product);
    }

    public Product update(Product product) {
        return productRepository.save(product);
    }

    public boolean delete(int id) {
        if (productRepository.existsById(id)) {
            productRepository.deleteById(id);
            return true;
        }
        return false;
    }
}
```

### 5. gRPC Server Implementation (ProductServiceImpl.java)

**Description:** gRPC server implementation that exposes product operations as gRPC services. This class implements the ProductService gRPC interface defined in the .proto file.

**Key Features:**

- `@GrpcService` annotation registers this as a gRPC service
- Extends `ProductServiceImplBase` generated from .proto file
- Implements all RPC methods: GetProductById, GetAllProducts, CreateProduct, UpdateProduct, DeleteProduct
- Uses `StreamObserver` for asynchronous response handling
- Includes proper error handling with gRPC Status codes
- Converts between JPA entities and Protocol Buffer messages
- Integrates with Spring's ProductService for business logic

```java
package com.example.grpc.server;

import com.example.demo.service.ProductService;
import com.example.demo.entity.Product;
import com.example.grpc.ProductServiceProto;
import com.example.grpc.ProductServiceProto.*;
import com.example.grpc.ProductServiceGrpc;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import net.devh.boot.grpc.server.service.GrpcService;

import java.util.List;
import java.util.stream.Collectors;

@GrpcService
public class ProductServiceImpl extends ProductServiceGrpc.ProductServiceImplBase {

    private final ProductService productService;

    public ProductServiceImpl(ProductService productService) {
        this.productService = productService;
    }

    @Override
    public void getProductById(ProductIdRequest request, StreamObserver<ProductResponse> responseObserver) {
        productService.getById(request.getId()).ifPresentOrElse(product -> {
            responseObserver.onNext(ProductResponse.newBuilder().setProduct(convertToProto(product)).build());
            responseObserver.onCompleted();
        }, () -> {
            responseObserver.onError(Status.NOT_FOUND.withDescription("Product with ID " + request.getId() + " not found").asRuntimeException());
        });
    }

    @Override
    public void getAllProducts(Empty request, StreamObserver<ProductsResponse> responseObserver) {
        List<Product> products = productService.getAll();
        ProductsResponse response = ProductsResponse.newBuilder()
                .addAllProducts(products.stream().map(this::convertToProto).collect(Collectors.toList()))
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void createProduct(CreateProductRequest request, StreamObserver<ProductResponse> responseObserver) {
        Product product = new Product();
        product.setName(request.getName());
        product.setDescription(request.getDescription());
        product.setPrice(request.getPrice());

        Product created = productService.create(product);
        responseObserver.onNext(ProductResponse.newBuilder().setProduct(convertToProto(created)).build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateProduct(UpdateProductRequest request, StreamObserver<ProductResponse> responseObserver) {
        productService.getById(request.getId()).ifPresentOrElse(existing -> {
            existing.setName(request.getName());
            existing.setDescription(request.getDescription());
            existing.setPrice(request.getPrice());

            Product updated = productService.update(existing);
            responseObserver.onNext(ProductResponse.newBuilder().setProduct(convertToProto(updated)).build());
            responseObserver.onCompleted();
        }, () -> {
            responseObserver.onError(Status.NOT_FOUND.withDescription("Product not found").asRuntimeException());
        });
    }

    @Override
    public void deleteProduct(ProductIdRequest request, StreamObserver<DeleteResponse> responseObserver) {
        boolean deleted = productService.delete(request.getId());
        DeleteResponse response = DeleteResponse.newBuilder()
                .setSuccess(deleted)
                .setMessage(deleted ? "Deleted successfully" : "Product not found")
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    private ProductServiceProto.Product convertToProto(Product product) {
        return ProductServiceProto.Product.newBuilder()
                .setId(product.getId())
                .setName(product.getName())
                .setDescription(product.getDescription())
                .setPrice(product.getPrice())
                .build();
    }
}
```

### 6. gRPC Client (ProductGrpcClient.java)

**Description:** gRPC client that communicates with the gRPC server. This client is used by the REST controller to make gRPC calls to the server.

**Key Features:**

- `@Service` annotation makes it a Spring-managed bean
- Creates and manages gRPC channel and blocking stub
- Uses `@Value` annotations to inject configuration from application.properties
- Provides methods that mirror the gRPC service operations
- Handles Protocol Buffer message creation and parsing
- Converts between Protocol Buffer messages and JPA entities
- Uses blocking stub for synchronous communication

```java
package com.example.grpc.client;

import com.example.grpc.ProductServiceGrpc;
import com.example.grpc.ProductServiceProto;
import com.example.grpc.ProductServiceProto.*;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.example.demo.entity.Product;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ProductGrpcClient {

    private final ManagedChannel channel;
    private final ProductServiceGrpc.ProductServiceBlockingStub blockingStub;

    public ProductGrpcClient(@Value("${grpc.server.host:localhost}") String host,
                             @Value("${grpc.server.port}") int port) {
        this.channel = ManagedChannelBuilder.forAddress(host, port)
                .usePlaintext()
                .build();
        this.blockingStub = ProductServiceGrpc.newBlockingStub(channel);
    }

    public ProductServiceProto.Product getProductById(int id) {
        ProductIdRequest request = ProductIdRequest.newBuilder().setId(id).build();
        ProductResponse response = blockingStub.getProductById(request);
        return response.getProduct();
    }

    public List<Product> getAllProducts() {
        ProductsResponse response = blockingStub.getAllProducts(Empty.newBuilder().build());
        return response.getProductsList().stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());
    }

    public ProductServiceProto.Product createProduct(String name, String description, double price) {
        CreateProductRequest request = CreateProductRequest.newBuilder()
                .setName(name)
                .setDescription(description)
                .setPrice(price)
                .build();
        ProductResponse response = blockingStub.createProduct(request);
        return response.getProduct();
    }

    public ProductServiceProto.Product updateProduct(int id, String name, String description, double price) {
        UpdateProductRequest request = UpdateProductRequest.newBuilder()
                .setId(id)
                .setName(name)
                .setDescription(description)
                .setPrice(price)
                .build();
        ProductResponse response = blockingStub.updateProduct(request);
        return response.getProduct();
    }

    public DeleteResponse deleteProduct(int id) {
        ProductIdRequest request = ProductIdRequest.newBuilder().setId(id).build();
        return blockingStub.deleteProduct(request);
    }

    private Product convertToEntity(ProductServiceProto.Product protoProduct) {
        Product product = new Product();
        product.setId(protoProduct.getId());
        product.setName(protoProduct.getName());
        product.setDescription(protoProduct.getDescription());
        product.setPrice(protoProduct.getPrice());
        return product;
    }
}
```

### 7. REST Controller (ProductController.java)

**Description:** REST API controller that provides HTTP endpoints for product operations. This controller uses the gRPC client internally to communicate with the gRPC server.

**Key Features:**

- `@RestController` annotation enables REST API functionality
- `@RequestMapping("/api/products")` sets base URL for all endpoints
- Provides standard REST endpoints: GET, POST, PUT, DELETE
- Uses gRPC client internally for all operations
- Converts between HTTP requests/responses and gRPC calls
- Includes inner `ProductRequest` class for request body mapping
- Demonstrates microservices architecture with REST-to-gRPC communication

```java
package com.example.demo.controller;

import com.example.grpc.ProductServiceProto;
import com.example.grpc.client.ProductGrpcClient;
import com.example.demo.entity.Product;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/products")
public class ProductController {

    private final ProductGrpcClient productGrpcClient;

    public ProductController(ProductGrpcClient productGrpcClient) {
        this.productGrpcClient = productGrpcClient;
    }

    @GetMapping("/{id}")
    public Product getProductById(@PathVariable int id) {
        ProductServiceProto.Product protoProduct = productGrpcClient.getProductById(id);
        return convertProtoToEntity(protoProduct);
    }

    @GetMapping
    public List<Product> getAllProducts() {
        return productGrpcClient.getAllProducts();
    }

    @PostMapping
    public Product createProduct(@RequestBody ProductRequest request) {
        ProductServiceProto.Product protoProduct = productGrpcClient.createProduct(
                request.getName(),
                request.getDescription(),
                request.getPrice()
        );
        return convertProtoToEntity(protoProduct);
    }

    @PutMapping("/{id}")
    public Product updateProduct(@PathVariable int id, @RequestBody ProductRequest request) {
        ProductServiceProto.Product protoProduct = productGrpcClient.updateProduct(
                id,
                request.getName(),
                request.getDescription(),
                request.getPrice()
        );
        return convertProtoToEntity(protoProduct);
    }

    @DeleteMapping("/{id}")
    public String deleteProduct(@PathVariable int id) {
        ProductServiceProto.DeleteResponse response = productGrpcClient.deleteProduct(id);
        return response.getSuccess() ? "Product deleted successfully" : "Failed to delete product";
    }

    private Product convertProtoToEntity(ProductServiceProto.Product proto) {
        Product product = new Product();
        product.setId(proto.getId());
        product.setName(proto.getName());
        product.setDescription(proto.getDescription());
        product.setPrice(proto.getPrice());
        return product;
    }

    public static class ProductRequest {
        private String name;
        private String description;
        private double price;

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public double getPrice() { return price; }
        public void setPrice(double price) { this.price = price; }
    }
}
```

## Build Configuration (pom.xml)

**Description:** Maven build configuration with all dependencies and plugins needed for the project.

**Key Features:**

- Spring Boot parent POM for dependency management
- All required dependencies for Spring Boot, JPA, MySQL, gRPC
- Protobuf Maven plugin for generating Java classes from .proto files
- OS Maven plugin for platform-specific protoc execution
- Proper version management for all gRPC and Protocol Buffer dependencies

## Prerequisites and Setup

### 1. System Requirements

- **Java 17** or higher
- **Maven 3.6+**
- **MySQL 8.0+**
- **Git** (for cloning)

### 2. Database Setup

```sql
-- Create database
CREATE DATABASE productsdb;

-- Create user (optional)
CREATE USER 'demo_user'@'localhost' IDENTIFIED BY 'demo_password';
GRANT ALL PRIVILEGES ON productsdb.* TO 'demo_user'@'localhost';
FLUSH PRIVILEGES;
```

## Detailed Build and Run Commands

### 1. Clone the Repository

```bash
git clone <repository-url>
cd demo
```

### 2. Generate gRPC Classes from Proto Files

**Description:** This step generates Java classes from the .proto file using the protobuf-maven-plugin.

```bash
# Generate protobuf and gRPC classes
mvn protobuf:compile
mvn protobuf:compile-custom

# Or generate both in one command
mvn clean compile
```

**Generated Files Location:**

- `target/generated-sources/protobuf/java/` - Protocol Buffer message classes
- `target/generated-sources/protobuf/grpc-java/` - gRPC service classes

### 3. Clean and Compile Project

```bash
# Clean previous builds
mvn clean

# Compile the project (includes protobuf generation)
mvn compile

# Compile and run tests
mvn test

# Package the application
mvn package

# Skip tests during packaging
mvn package -DskipTests
```

### 4. Run the Application

#### Option A: Using Maven Spring Boot Plugin

```bash
# Run with Maven (recommended for development)
mvn spring-boot:run

# Run with specific profile
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# Run with JVM arguments
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Xmx512m"
```

#### Option B: Using Java JAR

```bash
# First package the application
mvn clean package -DskipTests

# Run the JAR file
java -jar target/demo-0.0.1-SNAPSHOT.jar

# Run with specific profile
java -jar target/demo-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod

# Run with custom port
java -jar target/demo-0.0.1-SNAPSHOT.jar --server.port=8080
```

#### Option C: Using Maven Wrapper (if available)

```bash
# On Windows
.\mvnw.cmd spring-boot:run

# On Linux/Mac
./mvnw spring-boot:run
```

### 5. Verify Application Startup

The application will start with the following services:

- **REST API Server:** http://localhost:8088
- **gRPC Server:** localhost:9090

**Expected Console Output:**

```
Started DemoApplication in X.XXX seconds (JVM running for X.XXX)
gRPC Server started, listening on port 9090
```

### 6. Development Commands

#### Hot Reload (Development Mode)

```bash
# Enable Spring Boot DevTools for hot reload
mvn spring-boot:run -Dspring-boot.run.fork=false
```

#### Debug Mode

```bash
# Run in debug mode (port 5005)
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005"

# Or using Java
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -jar target/demo-0.0.1-SNAPSHOT.jar
```

#### Force Protobuf Regeneration

```bash
# Clean and regenerate protobuf classes
mvn clean protobuf:compile protobuf:compile-custom compile
```

## API Testing Examples

### REST API Endpoints

#### 1. Get All Products

```bash
curl -X GET http://localhost:8088/api/products
```

#### 2. Get Product by ID

```bash
curl -X GET http://localhost:8088/api/products/1
```

#### 3. Create New Product

```bash
curl -X POST http://localhost:8088/api/products \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Laptop",
    "description": "High-performance laptop",
    "price": 999.99
  }'
```

#### 4. Update Product

```bash
curl -X PUT http://localhost:8088/api/products/1 \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Gaming Laptop",
    "description": "High-performance gaming laptop",
    "price": 1299.99
  }'
```

#### 5. Delete Product

```bash
curl -X DELETE http://localhost:8088/api/products/1
```

### gRPC Testing with grpcurl

#### Install grpcurl

```bash
# On macOS
brew install grpcurl

# On Windows (using Chocolatey)
choco install grpcurl

# Or download from: https://github.com/fullstorydev/grpcurl/releases
```

#### gRPC API Examples

#### 1. List Available Services

```bash
grpcurl -plaintext localhost:9090 list
```

#### 2. Get Service Methods

```bash
grpcurl -plaintext localhost:9090 list com.example.grpc.ProductService
```

#### 3. Get All Products

```bash
grpcurl -plaintext localhost:9090 com.example.grpc.ProductService/GetAllProducts
```

#### 4. Get Product by ID

```bash
grpcurl -plaintext -d '{"id": 1}' localhost:9090 com.example.grpc.ProductService/GetProductById
```

#### 5. Create Product

```bash
grpcurl -plaintext -d '{
  "name": "Smartphone",
  "description": "Latest smartphone",
  "price": 699.99
}' localhost:9090 com.example.grpc.ProductService/CreateProduct
```

#### 6. Update Product

```bash
grpcurl -plaintext -d '{
  "id": 1,
  "name": "Updated Smartphone",
  "description": "Updated latest smartphone",
  "price": 799.99
}' localhost:9090 com.example.grpc.ProductService/UpdateProduct
```

#### 7. Delete Product

```bash
grpcurl -plaintext -d '{"id": 1}' localhost:9090 com.example.grpc.ProductService/DeleteProduct
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Port Already in Use

**Error:** `Port 8088 was already in use`
**Solution:**

```bash
# Find process using the port
netstat -ano | findstr :8088  # Windows
lsof -i :8088                 # Linux/Mac

# Kill the process
taskkill /PID <PID> /F        # Windows
kill -9 <PID>                 # Linux/Mac

# Or change port in application.properties
server.port=8089
```

#### 2. MySQL Connection Issues

**Error:** `Communications link failure`
**Solutions:**

- Ensure MySQL is running
- Check database credentials in application.properties
- Verify database exists: `CREATE DATABASE productsdb;`
- Check MySQL port (default 3306)

#### 3. Protobuf Generation Issues

**Error:** `protoc not found` or compilation errors
**Solutions:**

```bash
# Clean and regenerate
mvn clean
mvn protobuf:compile protobuf:compile-custom

# Check Maven version (requires 3.6+)
mvn --version

# Verify protobuf plugin configuration in pom.xml
```

#### 4. gRPC Server Not Starting

**Error:** gRPC server fails to start
**Solutions:**

- Check if port 9090 is available
- Verify gRPC dependencies in pom.xml
- Check application.properties for gRPC configuration
- Ensure @GrpcService annotation is present

#### 5. Class Not Found Exceptions

**Error:** `ClassNotFoundException` for generated classes
**Solutions:**

```bash
# Regenerate protobuf classes
mvn clean compile

# Check if generated classes exist in target/generated-sources/
# Refresh IDE project if using Eclipse/IntelliJ
```

## Project Architecture

### Architecture Overview

```
┌─────────────────┐    HTTP     ┌─────────────────┐    gRPC     ┌─────────────────┐
│   REST Client   │ ──────────► │ ProductController│ ──────────► │ ProductGrpcClient│
│  (Postman/curl) │             │   (REST API)    │             │                 │
└─────────────────┘             └─────────────────┘             └─────────────────┘
                                                                          │
                                                                          │ gRPC Call
                                                                          ▼
┌─────────────────┐             ┌─────────────────┐             ┌─────────────────┐
│     MySQL       │◄────────────│ ProductService  │◄────────────│ProductServiceImpl│
│   Database      │   JPA/SQL   │ (Business Logic)│  Injection  │  (gRPC Server)  │
└─────────────────┘             └─────────────────┘             └─────────────────┘
                                          ▲
                                          │ JPA
                                          ▼
                                ┌─────────────────┐
                                │ProductRepository│
                                │   (Data Layer)  │
                                └─────────────────┘
```

### Component Responsibilities

#### 1. **ProductController (REST Layer)**

- Exposes HTTP REST endpoints
- Handles HTTP request/response mapping
- Converts between HTTP and gRPC protocols
- Acts as API gateway for external clients

#### 2. **ProductGrpcClient (gRPC Client)**

- Manages gRPC channel and connection
- Converts method calls to gRPC requests
- Handles Protocol Buffer serialization/deserialization
- Provides type-safe gRPC communication

#### 3. **ProductServiceImpl (gRPC Server)**

- Implements gRPC service interface
- Handles incoming gRPC requests
- Manages StreamObserver for responses
- Converts between entities and Protocol Buffers

#### 4. **ProductService (Business Layer)**

- Contains business logic and validation
- Manages transactions
- Provides clean interface for data operations
- Decouples presentation from data access

#### 5. **ProductRepository (Data Layer)**

- Handles database operations
- Provides CRUD functionality
- Manages JPA entity lifecycle
- Abstracts database implementation details

#### 6. **Product Entity (Data Model)**

- Represents database table structure
- Defines JPA mappings and relationships
- Contains validation annotations
- Provides data transfer object functionality

### Communication Flow

#### REST API Request Flow:

1. **HTTP Request** → ProductController
2. **Method Call** → ProductGrpcClient
3. **gRPC Request** → ProductServiceImpl
4. **Business Logic** → ProductService
5. **Database Query** → ProductRepository
6. **JPA/SQL** → MySQL Database

#### Response Flow (Reverse):

1. **Database Result** → ProductRepository
2. **Entity Object** → ProductService
3. **Business Result** → ProductServiceImpl
4. **gRPC Response** → ProductGrpcClient
5. **Method Return** → ProductController
6. **HTTP Response** → Client

### Key Design Patterns

#### 1. **Repository Pattern**

- `ProductRepository` abstracts data access
- Provides clean separation between business and data layers
- Enables easy testing with mock repositories

#### 2. **Service Layer Pattern**

- `ProductService` encapsulates business logic
- Provides transaction boundaries
- Enables reusability across different presentation layers

#### 3. **Client-Server Pattern**

- gRPC client-server communication
- Separation of concerns between REST and gRPC layers
- Enables distributed system architecture

#### 4. **Dependency Injection**

- Spring manages all component dependencies
- Constructor injection for immutable dependencies
- Enables loose coupling and testability

## Performance Considerations

### gRPC Advantages

- **Binary Protocol:** Faster than JSON/XML
- **HTTP/2:** Multiplexing, header compression
- **Type Safety:** Compile-time validation
- **Streaming:** Supports real-time communication

### Optimization Tips

- Use connection pooling for database
- Implement caching for frequently accessed data
- Consider async processing for heavy operations
- Monitor gRPC channel health

## Security Considerations

### Current Implementation

- Basic HTTP and gRPC communication
- No authentication/authorization implemented
- Database credentials in plain text

### Production Recommendations

- Implement JWT authentication
- Use TLS for gRPC communication
- Encrypt database passwords
- Add input validation and sanitization
- Implement rate limiting
- Use HTTPS for REST endpoints

## Conclusion

This project demonstrates a complete Spring Boot application with gRPC integration, showcasing:

- **Modern Architecture:** Clean separation of concerns with layered architecture
- **Protocol Integration:** Seamless REST-to-gRPC communication
- **Code Generation:** Automatic Java class generation from Protocol Buffers
- **Database Integration:** JPA/Hibernate with MySQL
- **Development Tools:** Hot reload, debugging, and testing capabilities
- **Production Ready:** Comprehensive build and deployment configuration

The project serves as a solid foundation for building microservices with gRPC communication while maintaining REST API compatibility for external clients.

### Next Steps for Enhancement

1. Add authentication and authorization
2. Implement caching (Redis)
3. Add monitoring and metrics (Micrometer/Prometheus)
4. Implement distributed tracing
5. Add comprehensive unit and integration tests
6. Configure Docker containerization
7. Set up CI/CD pipeline
8. Add API documentation (OpenAPI/Swagger)
