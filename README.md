# مشروع Spring Boot مع gRPC - إدارة المنتجات

## نظرة عامة
هذا المشروع عبارة عن تطبيق Spring Boot يستخدم gRPC للتواصل بين الخدمات، مع قاعدة بيانات MySQL لإدارة المنتجات. يوفر المشروع REST API و gRPC API لعمليات CRUD على المنتجات.

## هيكل المشروع
```
demo/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── example/
│   │   │           ├── demo/
│   │   │           │   ├── DemoApplication.java
│   │   │           │   ├── controller/
│   │   │           │   │   └── ProductController.java
│   │   │           │   ├── entity/
│   │   │           │   │   └── Product.java
│   │   │           │   ├── repos/
│   │   │           │   │   └── ProductRepository.java
│   │   │           │   └── service/
│   │   │           │       └── ProductService.java
│   │   │           └── grpc/
│   │   │               ├── client/
│   │   │               │   └── ProductGrpcClient.java
│   │   │               └── server/
│   │   │                   └── ProductServiceImpl.java
│   │   ├── proto/
│   │   │   └── products.proto
│   │   └── resources/
│   │       └── application.properties
│   └── test/
├── target/
├── pom.xml
├── mvnw
├── mvnw.cmd
└── README.md
```

## التقنيات المستخدمة

### إطار العمل الأساسي
- **Spring Boot 3.5.3** - إطار العمل الرئيسي
- **Java 17** - لغة البرمجة
- **Maven** - إدارة التبعيات والبناء

### قاعدة البيانات
- **MySQL** - قاعدة البيانات الرئيسية
- **Spring Data JPA** - للتعامل مع قاعدة البيانات
- **Hibernate** - ORM

### gRPC
- **gRPC 1.62.2** - للتواصل بين الخدمات
- **Protocol Buffers 3.25.1** - لتسلسل البيانات
- **grpc-spring-boot-starter 2.15.0** - تكامل gRPC مع Spring Boot

### أدوات إضافية
- **Lombok** - لتقليل الكود المكرر
- **Spring Boot DevTools** - للتطوير السريع

## التبعيات (Dependencies)

### التبعيات الأساسية
```xml
<!-- Spring Boot Starters -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-jpa</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>

<!-- Database -->
<dependency>
    <groupId>com.mysql</groupId>
    <artifactId>mysql-connector-j</artifactId>
    <scope>runtime</scope>
</dependency>

<!-- Development Tools -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-devtools</artifactId>
    <scope>runtime</scope>
    <optional>true</optional>
</dependency>
<dependency>
    <groupId>org.projectlombok</groupId>
    <artifactId>lombok</artifactId>
    <optional>true</optional>
</dependency>

<!-- Testing -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>
```

### تبعيات gRPC
```xml
<!-- gRPC Core Dependencies -->
<dependency>
    <groupId>io.grpc</groupId>
    <artifactId>grpc-netty-shaded</artifactId>
    <version>1.62.2</version>
</dependency>
<dependency>
    <groupId>io.grpc</groupId>
    <artifactId>grpc-protobuf</artifactId>
    <version>1.62.2</version>
</dependency>
<dependency>
    <groupId>io.grpc</groupId>
    <artifactId>grpc-stub</artifactId>
    <version>1.62.2</version>
</dependency>

<!-- Protocol Buffers -->
<dependency>
    <groupId>com.google.protobuf</groupId>
    <artifactId>protobuf-java</artifactId>
    <version>3.25.1</version>
</dependency>
<dependency>
    <groupId>com.google.protobuf</groupId>
    <artifactId>protobuf-java-util</artifactId>
    <version>3.25.1</version>
</dependency>

<!-- Annotations -->
<dependency>
    <groupId>javax.annotation</groupId>
    <artifactId>javax.annotation-api</artifactId>
    <version>1.3.2</version>
</dependency>

<!-- Spring Boot gRPC Integration -->
<dependency>
    <groupId>net.devh</groupId>
    <artifactId>grpc-spring-boot-starter</artifactId>
    <version>2.15.0.RELEASE</version>
</dependency>
```

## إعدادات التطبيق (application.properties)
```properties
# اسم التطبيق
spring.application.name=demo

# إعدادات قاعدة البيانات
spring.datasource.url=**************************************
spring.datasource.username=root
spring.datasource.password=!@#Zayed

# إعدادات خادم الويب
server.port=8088

# إعدادات خادم gRPC
grpc.server.port=9090
grpc.server.address=0.0.0.0

# إعدادات عميل gRPC
grpc.server.host=localhost
```

## ملف Protocol Buffers (products.proto)
```protobuf
syntax = "proto3";

option java_package = "com.example.grpc";
option java_outer_classname = "ProductServiceProto";

service ProductService {
  rpc GetProductById(ProductIdRequest) returns (ProductResponse);
  rpc GetAllProducts(Empty) returns (ProductsResponse);
  rpc CreateProduct(CreateProductRequest) returns (ProductResponse);
  rpc UpdateProduct(UpdateProductRequest) returns (ProductResponse);
  rpc DeleteProduct(ProductIdRequest) returns (DeleteResponse);
}

message Empty {}

message ProductIdRequest {
  int32 id = 1;
}

message Product {
  int32 id = 1;
  string name = 2;
  string description = 3;
  double price = 4;
}

message ProductResponse {
  Product product = 1;
}

message ProductsResponse {
  repeated Product products = 1;
}

message CreateProductRequest {
  string name = 1;
  string description = 2;
  double price = 3;
}

message UpdateProductRequest {
  int32 id = 1;
  string name = 2;
  string description = 3;
  double price = 4;
}

message DeleteResponse {
  bool success = 1;
  string message = 2;
}
```
