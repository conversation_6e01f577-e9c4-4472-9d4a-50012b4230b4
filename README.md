# Spring Boot with gRPC - Product Management System

[![Java](https://img.shields.io/badge/Java-17-orange.svg)](https://www.oracle.com/java/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.5.3-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![gRPC](https://img.shields.io/badge/gRPC-1.62.2-blue.svg)](https://grpc.io/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0-blue.svg)](https://www.mysql.com/)

## Overview

This project is a Spring Boot application that uses gRPC for inter-service communication, with MySQL database for product management. The project provides both REST API and gRPC API for CRUD operations on products.

## Table of Contents

- [Overview](#overview)
- [Project Structure](#project-structure)
- [Technologies Used](#technologies-used)
- [Dependencies](#dependencies)
- [Application Configuration](#application-configuration)
- [Protocol Buffers Definition](#protocol-buffers-definition)
- [Recent Updates and Improvements](#recent-updates-and-improvements)
- [Complete Source Code](#complete-source-code)
- [Build Configuration](#build-configuration)
- [Prerequisites and Setup](#prerequisites-and-setup)
- [Detailed Build and Run Commands](#detailed-build-and-run-commands)
- [API Testing Examples](#api-testing-examples)
- [Troubleshooting](#troubleshooting)
- [Project Architecture](#project-architecture)
- [Performance Considerations](#performance-considerations)
- [Security Considerations](#security-considerations)
- [Conclusion](#conclusion)

## Project Structure

```
demo/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── example/
│   │   │           ├── demo/
│   │   │           │   ├── DemoApplication.java
│   │   │           │   ├── controller/
│   │   │           │   │   └── ProductController.java
│   │   │           │   ├── entity/
│   │   │           │   │   └── Product.java
│   │   │           │   ├── repos/
│   │   │           │   │   └── ProductRepository.java
│   │   │           │   └── service/
│   │   │           │       └── ProductService.java
│   │   │           └── grpc/
│   │   │               ├── client/
│   │   │               │   └── ProductGrpcClient.java
│   │   │               └── server/
│   │   │                   └── ProductServiceImpl.java
│   │   ├── proto/
│   │   │   └── products.proto
│   │   └── resources/
│   │       └── application.properties
│   └── test/
├── target/
├── pom.xml
├── mvnw
├── mvnw.cmd
└── README.md
```

## Technologies Used

### Core Framework

- **Spring Boot 3.5.3** - Main framework
- **Java 17** - Programming language
- **Maven** - Dependency management and build tool

### Database

- **MySQL** - Primary database
- **Spring Data JPA** - Database interaction
- **Hibernate** - ORM

### gRPC

- **gRPC 1.62.2** - Inter-service communication
- **Protocol Buffers 3.25.1** - Data serialization
- **grpc-spring-boot-starter 2.15.0** - gRPC Spring Boot integration

### Additional Tools

- **Lombok** - Reduce boilerplate code
- **Spring Boot DevTools** - Development tools

## Dependencies

### Core Dependencies

```xml
<!-- Spring Boot Starters -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-jpa</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>

<!-- Database -->
<dependency>
    <groupId>com.mysql</groupId>
    <artifactId>mysql-connector-j</artifactId>
    <scope>runtime</scope>
</dependency>

<!-- Development Tools -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-devtools</artifactId>
    <scope>runtime</scope>
    <optional>true</optional>
</dependency>
<dependency>
    <groupId>org.projectlombok</groupId>
    <artifactId>lombok</artifactId>
    <optional>true</optional>
</dependency>

<!-- Testing -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>
```

### gRPC Dependencies

```xml
<!-- gRPC Core Dependencies -->
<dependency>
    <groupId>io.grpc</groupId>
    <artifactId>grpc-netty-shaded</artifactId>
    <version>1.62.2</version>
</dependency>
<dependency>
    <groupId>io.grpc</groupId>
    <artifactId>grpc-protobuf</artifactId>
    <version>1.62.2</version>
</dependency>
<dependency>
    <groupId>io.grpc</groupId>
    <artifactId>grpc-stub</artifactId>
    <version>1.62.2</version>
</dependency>

<!-- Protocol Buffers -->
<dependency>
    <groupId>com.google.protobuf</groupId>
    <artifactId>protobuf-java</artifactId>
    <version>3.25.1</version>
</dependency>
<dependency>
    <groupId>com.google.protobuf</groupId>
    <artifactId>protobuf-java-util</artifactId>
    <version>3.25.1</version>
</dependency>

<!-- Annotations -->
<dependency>
    <groupId>javax.annotation</groupId>
    <artifactId>javax.annotation-api</artifactId>
    <version>1.3.2</version>
</dependency>

<!-- Spring Boot gRPC Integration -->
<dependency>
    <groupId>net.devh</groupId>
    <artifactId>grpc-spring-boot-starter</artifactId>
    <version>2.15.0.RELEASE</version>
</dependency>
```

## Application Configuration (application.properties)

```properties
# Application name
spring.application.name=demo

# Database configuration
spring.datasource.url=**************************************
spring.datasource.username=root
spring.datasource.password=!@#Zayed

# Web server configuration
server.port=8088

# gRPC server configuration
grpc.server.port=9090
grpc.server.address=0.0.0.0

# gRPC client configuration
grpc.server.host=localhost
```

## Protocol Buffers Definition (products.proto)

**Description:** Simplified Protocol Buffer definition with cleaner message structure and more intuitive service method names.

**Key Updates:**

- Simplified service method names (GetById, GetAll, Create, Update, Delete)
- Unified Product message for both requests and responses
- Cleaner message structure with fewer redundant messages
- More intuitive naming conventions

```protobuf
syntax = "proto3";

option java_package = "com.example.grpc";
option java_outer_classname = "ProductServiceProto";

service ProductService {
  rpc GetById(Id) returns (Product);
  rpc GetAll(Empty) returns (ProductList);
  rpc Create(Product) returns (Product);
  rpc Update(Product) returns (Product);
  rpc Delete(Id) returns (Result);
}

message Empty {}

message Id {
  int32 id = 1;
}

message Product {
  int32 id = 1;
  string name = 2;
  string description = 3;
  double price = 4;
}

message ProductList {
  repeated Product products = 1;
}

message Result {
  bool success = 1;
  string message = 2;
}
```

## Recent Updates and Improvements

### Version 2.0 - Simplified Architecture

**Major Changes:**

- **Simplified Protocol Buffers:** Reduced message complexity with cleaner naming conventions
- **Streamlined gRPC Methods:** Updated service methods to use more intuitive names (GetById, GetAll, Create, Update, Delete)
- **Cleaner Code Structure:** Removed redundant wrapper classes and simplified method signatures
- **Direct Entity Usage:** REST controller now directly uses Product entities instead of separate request/response classes
- **Improved Error Handling:** More concise error handling in gRPC server implementation

**Benefits:**

- **Reduced Complexity:** Fewer message types and simpler method signatures
- **Better Maintainability:** Cleaner code structure with less boilerplate
- **Improved Performance:** Direct entity usage reduces object creation overhead
- **Enhanced Developer Experience:** More intuitive API design and method names

## Complete Source Code

### 1. Main Application Class (DemoApplication.java)

**Description:** This is the main entry point of the Spring Boot application. It configures component scanning for both the demo package and gRPC package to ensure all beans are properly detected and initialized.

**Key Features:**

- `@SpringBootApplication` annotation enables auto-configuration, component scanning, and configuration
- `scanBasePackages` explicitly includes both demo and gRPC packages
- `main` method starts the Spring Boot application

```java
package com.example.demo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication(scanBasePackages = {"com.example.demo", "com.example.grpc"})
public class DemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(DemoApplication.class, args);
    }
}
```

### 2. Product Entity (Product.java)

**Description:** JPA entity class representing a product in the database. This class defines the structure and mapping for the products table.

**Key Features:**

- `@Entity` annotation marks this as a JPA entity
- `@Id` and `@GeneratedValue` configure auto-incrementing primary key
- Contains product attributes: id, name, description, price
- Provides both parameterized and default constructors
- Includes standard getter and setter methods for all fields

```java
package com.example.demo.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;

@Entity
public class Product {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    private String name;
    private String description;
    private double price;

    public Product(int id, String name, String description, double price) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.price = price;
    }

    public Product() {
    }

    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public double getPrice() { return price; }
    public void setPrice(double price) { this.price = price; }
}
```

### 3. Product Repository (ProductRepository.java)

**Description:** Spring Data JPA repository interface for Product entity. Provides automatic implementation of common database operations without writing boilerplate code.

**Key Features:**

- Extends `JpaRepository<Product, Integer>` for automatic CRUD operations
- Inherits methods like `findAll()`, `findById()`, `save()`, `deleteById()`, etc.
- Spring Data JPA automatically generates implementation at runtime
- No custom methods needed for basic operations

```java
package com.example.demo.repos;

import com.example.demo.entity.Product;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ProductRepository extends JpaRepository<Product, Integer> {
}
```

### 4. Product Service (ProductService.java)

**Description:** Business logic layer that handles product operations. Acts as an intermediary between the repository layer and the presentation layer (controllers/gRPC services).

**Key Features:**

- `@Service` annotation marks this as a Spring service component
- Constructor injection for ProductRepository dependency
- Provides business methods for all CRUD operations
- `getById()` returns Optional to handle non-existent products gracefully
- `delete()` method includes existence check and returns boolean for success/failure
- Encapsulates business logic and data access operations

```java
package com.example.demo.service;

import com.example.demo.entity.Product;
import com.example.demo.repos.ProductRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class ProductService {

    private final ProductRepository productRepository;

    public ProductService(ProductRepository repo) {
        this.productRepository = repo;
    }

    public Optional<Product> getById(int id) {
        return productRepository.findById(id);
    }

    public List<Product> getAll() {
        return productRepository.findAll();
    }

    public Product create(Product product) {
        return productRepository.save(product);
    }

    public Product update(Product product) {
        return productRepository.save(product);
    }

    public boolean delete(int id) {
        if (productRepository.existsById(id)) {
            productRepository.deleteById(id);
            return true;
        }
        return false;
    }
}
```

### 5. gRPC Server Implementation (ProductServiceImpl.java)

**Description:** Updated gRPC server implementation with simplified method names and cleaner code structure. This class implements the ProductService gRPC interface defined in the updated .proto file.

**Key Features:**

- `@GrpcService` annotation registers this as a gRPC service
- Extends `ProductServiceImplBase` generated from .proto file
- Implements simplified RPC methods: GetById, GetAll, Create, Update, Delete
- Uses `StreamObserver` for asynchronous response handling
- Includes proper error handling with gRPC Status codes
- Converts between JPA entities and Protocol Buffer messages
- Cleaner helper methods for conversion between entities and proto messages

```java
package com.example.grpc.server;

import com.example.demo.service.ProductService;
import com.example.demo.entity.Product;
import com.example.grpc.ProductServiceProto;
import com.example.grpc.ProductServiceGrpc;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import net.devh.boot.grpc.server.service.GrpcService;

@GrpcService
public class ProductServiceImpl extends ProductServiceGrpc.ProductServiceImplBase {

    private final ProductService productService;

    public ProductServiceImpl(ProductService service) {
        this.productService = service;
    }

    private ProductServiceProto.Product toProto(Product p) {
        return ProductServiceProto.Product.newBuilder()
                .setId(p.getId())
                .setName(p.getName())
                .setDescription(p.getDescription())
                .setPrice(p.getPrice())
                .build();
    }

    private Product fromProto(ProductServiceProto.Product p) {
        Product product = new Product();
        product.setId(p.getId());
        product.setName(p.getName());
        product.setDescription(p.getDescription());
        product.setPrice(p.getPrice());
        return product;
    }

    @Override
    public void getById(ProductServiceProto.Id request, StreamObserver<ProductServiceProto.Product> responseObserver) {
        productService.getById(request.getId()).ifPresentOrElse(
                p -> {
                    responseObserver.onNext(toProto(p));
                    responseObserver.onCompleted();
                },
                () -> responseObserver.onError(Status.NOT_FOUND.asRuntimeException())
        );
    }

    @Override
    public void getAll(ProductServiceProto.Empty req, StreamObserver<ProductServiceProto.ProductList> obs) {
        var products = productService.getAll().stream().map(this::toProto).toList();
        obs.onNext(ProductServiceProto.ProductList.newBuilder().addAllProducts(products).build());
        obs.onCompleted();
    }

    @Override
    public void create(ProductServiceProto.Product req, StreamObserver<ProductServiceProto.Product> obs) {
        var created = productService.create(fromProto(req));
        obs.onNext(toProto(created));
        obs.onCompleted();
    }

    @Override
    public void update(ProductServiceProto.Product req, StreamObserver<ProductServiceProto.Product> obs) {
        var updated = productService.update(fromProto(req));
        obs.onNext(toProto(updated));
        obs.onCompleted();
    }

    @Override
    public void delete(ProductServiceProto.Id req, StreamObserver<ProductServiceProto.Result> obs) {
        boolean deleted = productService.delete(req.getId());
        obs.onNext(ProductServiceProto.Result.newBuilder()
                .setSuccess(deleted)
                .setMessage(deleted ? "Deleted" : "Not found").build());
        obs.onCompleted();
    }
}
```

### 6. gRPC Client (ProductGrpcClient.java)

**Description:** Updated gRPC client with simplified method names and cleaner code structure. This client communicates with the gRPC server using the updated protocol.

**Key Features:**

- `@Service` annotation makes it a Spring-managed bean
- Simplified channel management with direct stub creation
- Uses `@Value` annotations to inject configuration from application.properties
- Provides simplified methods that mirror the gRPC service operations
- Handles Protocol Buffer message creation and parsing
- Converts between Protocol Buffer messages and JPA entities
- Cleaner helper methods for entity conversion

```java
package com.example.grpc.client;

import com.example.grpc.ProductServiceGrpc;
import com.example.grpc.ProductServiceProto;
import io.grpc.ManagedChannelBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.example.demo.entity.Product;

import java.util.List;

@Service
public class ProductGrpcClient {

    private final ProductServiceGrpc.ProductServiceBlockingStub stub;

    public ProductGrpcClient(@Value("${grpc.server.host:localhost}") String host,
                             @Value("${grpc.server.port}") int port) {
        stub = ProductServiceGrpc.newBlockingStub(
                ManagedChannelBuilder.forAddress(host, port).usePlaintext().build());
    }

    public Product getById(int id) {
        return fromProto(stub.getById(ProductServiceProto.Id.newBuilder().setId(id).build()));
    }

    public List<Product> getAll() {
        return stub.getAll(ProductServiceProto.Empty.newBuilder().build())
                .getProductsList().stream().map(this::fromProto).toList();
    }

    public Product create(Product p) {
        return fromProto(stub.create(toProto(p)));
    }

    public Product update(Product p) {
        return fromProto(stub.update(toProto(p)));
    }

    public boolean delete(int id) {
        return stub.delete(ProductServiceProto.Id.newBuilder().setId(id).build()).getSuccess();
    }

    private Product fromProto(ProductServiceProto.Product proto) {
        Product p = new Product();
        p.setId(proto.getId());
        p.setName(proto.getName());
        p.setDescription(proto.getDescription());
        p.setPrice(proto.getPrice());
        return p;
    }

    private ProductServiceProto.Product toProto(Product p) {
        return ProductServiceProto.Product.newBuilder()
                .setId(p.getId())
                .setName(p.getName())
                .setDescription(p.getDescription())
                .setPrice(p.getPrice())
                .build();
    }
}
```

### 7. REST Controller (ProductController.java)

**Description:** Updated REST API controller with simplified method names and cleaner code structure. This controller uses the updated gRPC client to communicate with the gRPC server.

**Key Features:**

- `@RestController` annotation enables REST API functionality
- `@RequestMapping("/api/products")` sets base URL for all endpoints
- Provides standard REST endpoints: GET, POST, PUT, DELETE
- Uses updated gRPC client with simplified method names
- Direct Product entity usage for requests and responses
- Cleaner method names and simplified logic
- No need for separate request/response classes

```java
package com.example.demo.controller;

import com.example.grpc.client.ProductGrpcClient;
import com.example.demo.entity.Product;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/products")
public class ProductController {

    private final ProductGrpcClient client;

    public ProductController(ProductGrpcClient client) {
        this.client = client;
    }

    @GetMapping("/{id}")
    public Product get(@PathVariable int id) {
        return client.getById(id);
    }

    @GetMapping
    public List<Product> list() {
        return client.getAll();
    }

    @PostMapping
    public Product create(@RequestBody Product p) {
        return client.create(p);
    }

    @PutMapping("/{id}")
    public Product update(@PathVariable int id, @RequestBody Product p) {
        p.setId(id);
        return client.update(p);
    }

    @DeleteMapping("/{id}")
    public String delete(@PathVariable int id) {
        return client.delete(id) ? "Deleted" : "Not found";
    }
}
```

## Build Configuration (pom.xml)

**Description:** Maven build configuration with all dependencies and plugins needed for the project.

**Key Features:**

- Spring Boot parent POM for dependency management
- All required dependencies for Spring Boot, JPA, MySQL, gRPC
- Protobuf Maven plugin for generating Java classes from .proto files
- OS Maven plugin for platform-specific protoc execution
- Proper version management for all gRPC and Protocol Buffer dependencies

## Prerequisites and Setup

### 1. System Requirements

- **Java 17** or higher
- **Maven 3.6+**
- **MySQL 8.0+**
- **Git** (for cloning)

### 2. Database Setup

```sql
-- Create database
CREATE DATABASE productsdb;

-- Create user (optional)
CREATE USER 'demo_user'@'localhost' IDENTIFIED BY 'demo_password';
GRANT ALL PRIVILEGES ON productsdb.* TO 'demo_user'@'localhost';
FLUSH PRIVILEGES;
```

## Detailed Build and Run Commands

### 1. Clone the Repository

```bash
git clone <repository-url>
cd demo
```

### 2. Generate gRPC Classes from Proto Files

**Description:** This step generates Java classes from the .proto file using the protobuf-maven-plugin.

```bash
# Generate protobuf and gRPC classes
mvn protobuf:compile
mvn protobuf:compile-custom

# Or generate both in one command
mvn clean compile
```

**Generated Files Location:**

- `target/generated-sources/protobuf/java/` - Protocol Buffer message classes
- `target/generated-sources/protobuf/grpc-java/` - gRPC service classes

### 3. Clean and Compile Project

```bash
# Clean previous builds
mvn clean

# Compile the project (includes protobuf generation)
mvn compile

# Compile and run tests
mvn test

# Package the application
mvn package

# Skip tests during packaging
mvn package -DskipTests
```

### 4. Run the Application

#### Option A: Using Maven Spring Boot Plugin

```bash
# Run with Maven (recommended for development)
mvn spring-boot:run

# Run with specific profile
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# Run with JVM arguments
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Xmx512m"
```

#### Option B: Using Java JAR

```bash
# First package the application
mvn clean package -DskipTests

# Run the JAR file
java -jar target/demo-0.0.1-SNAPSHOT.jar

# Run with specific profile
java -jar target/demo-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod

# Run with custom port
java -jar target/demo-0.0.1-SNAPSHOT.jar --server.port=8080
```

#### Option C: Using Maven Wrapper (if available)

```bash
# On Windows
.\mvnw.cmd spring-boot:run

# On Linux/Mac
./mvnw spring-boot:run
```

### 5. Verify Application Startup

The application will start with the following services:

- **REST API Server:** http://localhost:8088
- **gRPC Server:** localhost:9090

**Expected Console Output:**

```
Started DemoApplication in X.XXX seconds (JVM running for X.XXX)
gRPC Server started, listening on port 9090
```

### 6. Development Commands

#### Hot Reload (Development Mode)

```bash
# Enable Spring Boot DevTools for hot reload
mvn spring-boot:run -Dspring-boot.run.fork=false
```

#### Debug Mode

```bash
# Run in debug mode (port 5005)
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005"

# Or using Java
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -jar target/demo-0.0.1-SNAPSHOT.jar
```

#### Force Protobuf Regeneration

```bash
# Clean and regenerate protobuf classes
mvn clean protobuf:compile protobuf:compile-custom compile
```

## API Testing Examples

### REST API Endpoints

#### 1. Get All Products

```bash
curl -X GET http://localhost:8088/api/products
```

#### 2. Get Product by ID

```bash
curl -X GET http://localhost:8088/api/products/1
```

#### 3. Create New Product

```bash
curl -X POST http://localhost:8088/api/products \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Laptop",
    "description": "High-performance laptop",
    "price": 999.99
  }'
```

#### 4. Update Product

```bash
curl -X PUT http://localhost:8088/api/products/1 \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Gaming Laptop",
    "description": "High-performance gaming laptop",
    "price": 1299.99
  }'
```

#### 5. Delete Product

```bash
curl -X DELETE http://localhost:8088/api/products/1
```

### gRPC Testing with grpcurl

#### Install grpcurl

```bash
# On macOS
brew install grpcurl

# On Windows (using Chocolatey)
choco install grpcurl

# Or download from: https://github.com/fullstorydev/grpcurl/releases
```

#### gRPC API Examples

#### 1. List Available Services

```bash
grpcurl -plaintext localhost:9090 list
```

#### 2. Get Service Methods

```bash
grpcurl -plaintext localhost:9090 list com.example.grpc.ProductService
```

#### 3. Get All Products

```bash
grpcurl -plaintext localhost:9090 com.example.grpc.ProductService/GetAll
```

#### 4. Get Product by ID

```bash
grpcurl -plaintext -d '{"id": 1}' localhost:9090 com.example.grpc.ProductService/GetById
```

#### 5. Create Product

```bash
grpcurl -plaintext -d '{
  "id": 0,
  "name": "Smartphone",
  "description": "Latest smartphone",
  "price": 699.99
}' localhost:9090 com.example.grpc.ProductService/Create
```

#### 6. Update Product

```bash
grpcurl -plaintext -d '{
  "id": 1,
  "name": "Updated Smartphone",
  "description": "Updated latest smartphone",
  "price": 799.99
}' localhost:9090 com.example.grpc.ProductService/Update
```

#### 7. Delete Product

```bash
grpcurl -plaintext -d '{"id": 1}' localhost:9090 com.example.grpc.ProductService/Delete
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Port Already in Use

**Error:** `Port 8088 was already in use`
**Solution:**

```bash
# Find process using the port
netstat -ano | findstr :8088  # Windows
lsof -i :8088                 # Linux/Mac

# Kill the process
taskkill /PID <PID> /F        # Windows
kill -9 <PID>                 # Linux/Mac

# Or change port in application.properties
server.port=8089
```

#### 2. MySQL Connection Issues

**Error:** `Communications link failure`
**Solutions:**

- Ensure MySQL is running
- Check database credentials in application.properties
- Verify database exists: `CREATE DATABASE productsdb;`
- Check MySQL port (default 3306)

#### 3. Protobuf Generation Issues

**Error:** `protoc not found` or compilation errors
**Solutions:**

```bash
# Clean and regenerate
mvn clean
mvn protobuf:compile protobuf:compile-custom

# Check Maven version (requires 3.6+)
mvn --version

# Verify protobuf plugin configuration in pom.xml
```

#### 4. gRPC Server Not Starting

**Error:** gRPC server fails to start
**Solutions:**

- Check if port 9090 is available
- Verify gRPC dependencies in pom.xml
- Check application.properties for gRPC configuration
- Ensure @GrpcService annotation is present

#### 5. Class Not Found Exceptions

**Error:** `ClassNotFoundException` for generated classes
**Solutions:**

```bash
# Regenerate protobuf classes
mvn clean compile

# Check if generated classes exist in target/generated-sources/
# Refresh IDE project if using Eclipse/IntelliJ
```

## Project Architecture

### Architecture Overview

```
┌─────────────────┐    HTTP     ┌─────────────────┐    gRPC     ┌─────────────────┐
│   REST Client   │ ──────────► │ ProductController│ ──────────► │ ProductGrpcClient│
│  (Postman/curl) │             │   (REST API)    │             │                 │
└─────────────────┘             └─────────────────┘             └─────────────────┘
                                                                          │
                                                                          │ gRPC Call
                                                                          ▼
┌─────────────────┐             ┌─────────────────┐             ┌─────────────────┐
│     MySQL       │◄────────────│ ProductService  │◄────────────│ProductServiceImpl│
│   Database      │   JPA/SQL   │ (Business Logic)│  Injection  │  (gRPC Server)  │
└─────────────────┘             └─────────────────┘             └─────────────────┘
                                          ▲
                                          │ JPA
                                          ▼
                                ┌─────────────────┐
                                │ProductRepository│
                                │   (Data Layer)  │
                                └─────────────────┘
```

### Component Responsibilities

#### 1. **ProductController (REST Layer)**

- Exposes HTTP REST endpoints
- Handles HTTP request/response mapping
- Converts between HTTP and gRPC protocols
- Acts as API gateway for external clients

#### 2. **ProductGrpcClient (gRPC Client)**

- Manages gRPC channel and connection
- Converts method calls to gRPC requests
- Handles Protocol Buffer serialization/deserialization
- Provides type-safe gRPC communication

#### 3. **ProductServiceImpl (gRPC Server)**

- Implements gRPC service interface
- Handles incoming gRPC requests
- Manages StreamObserver for responses
- Converts between entities and Protocol Buffers

#### 4. **ProductService (Business Layer)**

- Contains business logic and validation
- Manages transactions
- Provides clean interface for data operations
- Decouples presentation from data access

#### 5. **ProductRepository (Data Layer)**

- Handles database operations
- Provides CRUD functionality
- Manages JPA entity lifecycle
- Abstracts database implementation details

#### 6. **Product Entity (Data Model)**

- Represents database table structure
- Defines JPA mappings and relationships
- Contains validation annotations
- Provides data transfer object functionality

### Communication Flow

#### REST API Request Flow:

1. **HTTP Request** → ProductController
2. **Method Call** → ProductGrpcClient
3. **gRPC Request** → ProductServiceImpl
4. **Business Logic** → ProductService
5. **Database Query** → ProductRepository
6. **JPA/SQL** → MySQL Database

#### Response Flow (Reverse):

1. **Database Result** → ProductRepository
2. **Entity Object** → ProductService
3. **Business Result** → ProductServiceImpl
4. **gRPC Response** → ProductGrpcClient
5. **Method Return** → ProductController
6. **HTTP Response** → Client

### Key Design Patterns

#### 1. **Repository Pattern**

- `ProductRepository` abstracts data access
- Provides clean separation between business and data layers
- Enables easy testing with mock repositories

#### 2. **Service Layer Pattern**

- `ProductService` encapsulates business logic
- Provides transaction boundaries
- Enables reusability across different presentation layers

#### 3. **Client-Server Pattern**

- gRPC client-server communication
- Separation of concerns between REST and gRPC layers
- Enables distributed system architecture

#### 4. **Dependency Injection**

- Spring manages all component dependencies
- Constructor injection for immutable dependencies
- Enables loose coupling and testability

## Performance Considerations

### gRPC Advantages

- **Binary Protocol:** Faster than JSON/XML
- **HTTP/2:** Multiplexing, header compression
- **Type Safety:** Compile-time validation
- **Streaming:** Supports real-time communication

### Optimization Tips

- Use connection pooling for database
- Implement caching for frequently accessed data
- Consider async processing for heavy operations
- Monitor gRPC channel health

## Security Considerations

### Current Implementation

- Basic HTTP and gRPC communication
- No authentication/authorization implemented
- Database credentials in plain text

### Production Recommendations

- Implement JWT authentication
- Use TLS for gRPC communication
- Encrypt database passwords
- Add input validation and sanitization
- Implement rate limiting
- Use HTTPS for REST endpoints

## Conclusion

This project demonstrates a complete Spring Boot application with gRPC integration, showcasing modern microservices architecture with recent improvements:

### Key Achievements

- **Simplified Architecture:** Clean separation of concerns with streamlined layered architecture
- **Protocol Integration:** Seamless REST-to-gRPC communication with simplified message structure
- **Code Generation:** Automatic Java class generation from optimized Protocol Buffers
- **Database Integration:** JPA/Hibernate with MySQL using clean entity design
- **Development Tools:** Hot reload, debugging, and comprehensive testing capabilities
- **Production Ready:** Complete build and deployment configuration with detailed documentation

### Recent Improvements (Version 2.0)

- **Reduced Complexity:** Simplified Protocol Buffer definitions with fewer message types
- **Cleaner APIs:** More intuitive method names and direct entity usage
- **Better Performance:** Reduced object creation overhead and streamlined processing
- **Enhanced Maintainability:** Less boilerplate code and cleaner structure
- **Improved Developer Experience:** Simplified testing and debugging

The project serves as an excellent foundation for building modern microservices with gRPC communication while maintaining REST API compatibility for external clients.

### Next Steps for Enhancement

1. **Security:** Add JWT authentication and authorization
2. **Caching:** Implement Redis for improved performance
3. **Monitoring:** Add metrics with Micrometer/Prometheus
4. **Tracing:** Implement distributed tracing with Zipkin/Jaeger
5. **Testing:** Add comprehensive unit and integration tests
6. **Containerization:** Configure Docker and Kubernetes deployment
7. **CI/CD:** Set up automated build and deployment pipeline
8. **Documentation:** Add OpenAPI/Swagger for REST API documentation
9. **Validation:** Add input validation and error handling
10. **Logging:** Implement structured logging with correlation IDs
