# Spring Boot with gRPC - Product Management System

## Overview

This project is a Spring Boot application that uses gRPC for inter-service communication, with MySQL database for product management. The project provides both REST API and gRPC API for CRUD operations on products.

## Project Structure

```
demo/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── example/
│   │   │           ├── demo/
│   │   │           │   ├── DemoApplication.java
│   │   │           │   ├── controller/
│   │   │           │   │   └── ProductController.java
│   │   │           │   ├── entity/
│   │   │           │   │   └── Product.java
│   │   │           │   ├── repos/
│   │   │           │   │   └── ProductRepository.java
│   │   │           │   └── service/
│   │   │           │       └── ProductService.java
│   │   │           └── grpc/
│   │   │               ├── client/
│   │   │               │   └── ProductGrpcClient.java
│   │   │               └── server/
│   │   │                   └── ProductServiceImpl.java
│   │   ├── proto/
│   │   │   └── products.proto
│   │   └── resources/
│   │       └── application.properties
│   └── test/
├── target/
├── pom.xml
├── mvnw
├── mvnw.cmd
└── README.md
```

## Technologies Used

### Core Framework

- **Spring Boot 3.5.3** - Main framework
- **Java 17** - Programming language
- **Maven** - Dependency management and build tool

### Database

- **MySQL** - Primary database
- **Spring Data JPA** - Database interaction
- **Hibernate** - ORM

### gRPC

- **gRPC 1.62.2** - Inter-service communication
- **Protocol Buffers 3.25.1** - Data serialization
- **grpc-spring-boot-starter 2.15.0** - gRPC Spring Boot integration

### Additional Tools

- **Lombok** - Reduce boilerplate code
- **Spring Boot DevTools** - Development tools

## Dependencies

### Core Dependencies

```xml
<!-- Spring Boot Starters -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-jpa</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>

<!-- Database -->
<dependency>
    <groupId>com.mysql</groupId>
    <artifactId>mysql-connector-j</artifactId>
    <scope>runtime</scope>
</dependency>

<!-- Development Tools -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-devtools</artifactId>
    <scope>runtime</scope>
    <optional>true</optional>
</dependency>
<dependency>
    <groupId>org.projectlombok</groupId>
    <artifactId>lombok</artifactId>
    <optional>true</optional>
</dependency>

<!-- Testing -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>
```

### gRPC Dependencies

```xml
<!-- gRPC Core Dependencies -->
<dependency>
    <groupId>io.grpc</groupId>
    <artifactId>grpc-netty-shaded</artifactId>
    <version>1.62.2</version>
</dependency>
<dependency>
    <groupId>io.grpc</groupId>
    <artifactId>grpc-protobuf</artifactId>
    <version>1.62.2</version>
</dependency>
<dependency>
    <groupId>io.grpc</groupId>
    <artifactId>grpc-stub</artifactId>
    <version>1.62.2</version>
</dependency>

<!-- Protocol Buffers -->
<dependency>
    <groupId>com.google.protobuf</groupId>
    <artifactId>protobuf-java</artifactId>
    <version>3.25.1</version>
</dependency>
<dependency>
    <groupId>com.google.protobuf</groupId>
    <artifactId>protobuf-java-util</artifactId>
    <version>3.25.1</version>
</dependency>

<!-- Annotations -->
<dependency>
    <groupId>javax.annotation</groupId>
    <artifactId>javax.annotation-api</artifactId>
    <version>1.3.2</version>
</dependency>

<!-- Spring Boot gRPC Integration -->
<dependency>
    <groupId>net.devh</groupId>
    <artifactId>grpc-spring-boot-starter</artifactId>
    <version>2.15.0.RELEASE</version>
</dependency>
```

## Application Configuration (application.properties)

```properties
# Application name
spring.application.name=demo

# Database configuration
spring.datasource.url=**************************************
spring.datasource.username=root
spring.datasource.password=!@#Zayed

# Web server configuration
server.port=8088

# gRPC server configuration
grpc.server.port=9090
grpc.server.address=0.0.0.0

# gRPC client configuration
grpc.server.host=localhost
```

## Protocol Buffers Definition (products.proto)

```protobuf
syntax = "proto3";

option java_package = "com.example.grpc";
option java_outer_classname = "ProductServiceProto";

service ProductService {
  rpc GetProductById(ProductIdRequest) returns (ProductResponse);
  rpc GetAllProducts(Empty) returns (ProductsResponse);
  rpc CreateProduct(CreateProductRequest) returns (ProductResponse);
  rpc UpdateProduct(UpdateProductRequest) returns (ProductResponse);
  rpc DeleteProduct(ProductIdRequest) returns (DeleteResponse);
}

message Empty {}

message ProductIdRequest {
  int32 id = 1;
}

message Product {
  int32 id = 1;
  string name = 2;
  string description = 3;
  double price = 4;
}

message ProductResponse {
  Product product = 1;
}

message ProductsResponse {
  repeated Product products = 1;
}

message CreateProductRequest {
  string name = 1;
  string description = 2;
  double price = 3;
}

message UpdateProductRequest {
  int32 id = 1;
  string name = 2;
  string description = 3;
  double price = 4;
}

message DeleteResponse {
  bool success = 1;
  string message = 2;
}
```

## Complete Source Code

### 1. Main Application Class (DemoApplication.java)

```java
package com.example.demo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication(scanBasePackages = {"com.example.demo", "com.example.grpc"})
public class DemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(DemoApplication.class, args);
    }
}
```

### 2. Product Entity (Product.java)

```java
package com.example.demo.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;

@Entity
public class Product {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    private String name;
    private String description;
    private double price;

    public Product(int id, String name, String description, double price) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.price = price;
    }

    public Product() {
    }

    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public double getPrice() { return price; }
    public void setPrice(double price) { this.price = price; }
}
```

### 3. Product Repository (ProductRepository.java)

```java
package com.example.demo.repos;

import com.example.demo.entity.Product;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ProductRepository extends JpaRepository<Product, Integer> {
}
```

### 4. Product Service (ProductService.java)

```java
package com.example.demo.service;

import com.example.demo.entity.Product;
import com.example.demo.repos.ProductRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class ProductService {

    private final ProductRepository productRepository;

    public ProductService(ProductRepository repo) {
        this.productRepository = repo;
    }

    public Optional<Product> getById(int id) {
        return productRepository.findById(id);
    }

    public List<Product> getAll() {
        return productRepository.findAll();
    }

    public Product create(Product product) {
        return productRepository.save(product);
    }

    public Product update(Product product) {
        return productRepository.save(product);
    }

    public boolean delete(int id) {
        if (productRepository.existsById(id)) {
            productRepository.deleteById(id);
            return true;
        }
        return false;
    }
}
```

### 5. gRPC Server Implementation (ProductServiceImpl.java)

```java
package com.example.grpc.server;

import com.example.demo.service.ProductService;
import com.example.demo.entity.Product;
import com.example.grpc.ProductServiceProto;
import com.example.grpc.ProductServiceProto.*;
import com.example.grpc.ProductServiceGrpc;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import net.devh.boot.grpc.server.service.GrpcService;

import java.util.List;
import java.util.stream.Collectors;

@GrpcService
public class ProductServiceImpl extends ProductServiceGrpc.ProductServiceImplBase {

    private final ProductService productService;

    public ProductServiceImpl(ProductService productService) {
        this.productService = productService;
    }

    @Override
    public void getProductById(ProductIdRequest request, StreamObserver<ProductResponse> responseObserver) {
        productService.getById(request.getId()).ifPresentOrElse(product -> {
            responseObserver.onNext(ProductResponse.newBuilder().setProduct(convertToProto(product)).build());
            responseObserver.onCompleted();
        }, () -> {
            responseObserver.onError(Status.NOT_FOUND.withDescription("Product with ID " + request.getId() + " not found").asRuntimeException());
        });
    }

    @Override
    public void getAllProducts(Empty request, StreamObserver<ProductsResponse> responseObserver) {
        List<Product> products = productService.getAll();
        ProductsResponse response = ProductsResponse.newBuilder()
                .addAllProducts(products.stream().map(this::convertToProto).collect(Collectors.toList()))
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void createProduct(CreateProductRequest request, StreamObserver<ProductResponse> responseObserver) {
        Product product = new Product();
        product.setName(request.getName());
        product.setDescription(request.getDescription());
        product.setPrice(request.getPrice());

        Product created = productService.create(product);
        responseObserver.onNext(ProductResponse.newBuilder().setProduct(convertToProto(created)).build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateProduct(UpdateProductRequest request, StreamObserver<ProductResponse> responseObserver) {
        productService.getById(request.getId()).ifPresentOrElse(existing -> {
            existing.setName(request.getName());
            existing.setDescription(request.getDescription());
            existing.setPrice(request.getPrice());

            Product updated = productService.update(existing);
            responseObserver.onNext(ProductResponse.newBuilder().setProduct(convertToProto(updated)).build());
            responseObserver.onCompleted();
        }, () -> {
            responseObserver.onError(Status.NOT_FOUND.withDescription("Product not found").asRuntimeException());
        });
    }

    @Override
    public void deleteProduct(ProductIdRequest request, StreamObserver<DeleteResponse> responseObserver) {
        boolean deleted = productService.delete(request.getId());
        DeleteResponse response = DeleteResponse.newBuilder()
                .setSuccess(deleted)
                .setMessage(deleted ? "Deleted successfully" : "Product not found")
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    private ProductServiceProto.Product convertToProto(Product product) {
        return ProductServiceProto.Product.newBuilder()
                .setId(product.getId())
                .setName(product.getName())
                .setDescription(product.getDescription())
                .setPrice(product.getPrice())
                .build();
    }
}
```

### 6. gRPC Client (ProductGrpcClient.java)

```java
package com.example.grpc.client;

import com.example.grpc.ProductServiceGrpc;
import com.example.grpc.ProductServiceProto;
import com.example.grpc.ProductServiceProto.*;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.example.demo.entity.Product;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ProductGrpcClient {

    private final ManagedChannel channel;
    private final ProductServiceGrpc.ProductServiceBlockingStub blockingStub;

    public ProductGrpcClient(@Value("${grpc.server.host:localhost}") String host,
                             @Value("${grpc.server.port}") int port) {
        this.channel = ManagedChannelBuilder.forAddress(host, port)
                .usePlaintext()
                .build();
        this.blockingStub = ProductServiceGrpc.newBlockingStub(channel);
    }

    public ProductServiceProto.Product getProductById(int id) {
        ProductIdRequest request = ProductIdRequest.newBuilder().setId(id).build();
        ProductResponse response = blockingStub.getProductById(request);
        return response.getProduct();
    }

    public List<Product> getAllProducts() {
        ProductsResponse response = blockingStub.getAllProducts(Empty.newBuilder().build());
        return response.getProductsList().stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());
    }

    public ProductServiceProto.Product createProduct(String name, String description, double price) {
        CreateProductRequest request = CreateProductRequest.newBuilder()
                .setName(name)
                .setDescription(description)
                .setPrice(price)
                .build();
        ProductResponse response = blockingStub.createProduct(request);
        return response.getProduct();
    }

    public ProductServiceProto.Product updateProduct(int id, String name, String description, double price) {
        UpdateProductRequest request = UpdateProductRequest.newBuilder()
                .setId(id)
                .setName(name)
                .setDescription(description)
                .setPrice(price)
                .build();
        ProductResponse response = blockingStub.updateProduct(request);
        return response.getProduct();
    }

    public DeleteResponse deleteProduct(int id) {
        ProductIdRequest request = ProductIdRequest.newBuilder().setId(id).build();
        return blockingStub.deleteProduct(request);
    }

    private Product convertToEntity(ProductServiceProto.Product protoProduct) {
        Product product = new Product();
        product.setId(protoProduct.getId());
        product.setName(protoProduct.getName());
        product.setDescription(protoProduct.getDescription());
        product.setPrice(protoProduct.getPrice());
        return product;
    }
}
```
